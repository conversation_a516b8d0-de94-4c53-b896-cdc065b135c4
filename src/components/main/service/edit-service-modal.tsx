'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { updateService } from '@/actions/service';

const editServiceSchema = z.object({
  port: z.string().min(1, 'Port is required'),
  target_port: z.string().min(1, 'Target port is required'),
  type: z.enum(['ClusterIP', 'NodePort', 'LoadBalancer'], {
    required_error: 'Service type is required',
  }),
});

type EditServiceForm = z.infer<typeof editServiceSchema>;

interface EditServiceModalProps {
  service: {
    id: number;
    name: string;
    port: string;
    target_port: string;
    type: string;
    namespace_id: number;
    deployment_id: number;
    cluster_ip?: string;
    external_ip?: string;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditServiceModal({
  service,
  open,
  onOpenChange,
  onSuccess,
}: EditServiceModalProps) {
  const form = useForm<EditServiceForm>({
    resolver: zodResolver(editServiceSchema),
    defaultValues: {
      port: service.port,
      target_port: service.target_port,
      type: service.type as 'ClusterIP' | 'NodePort' | 'LoadBalancer',
    },
  });

  // Reset form when service changes
  useEffect(() => {
    if (service) {
      form.reset({
        port: service.port,
        target_port: service.target_port,
        type: service.type as 'ClusterIP' | 'NodePort' | 'LoadBalancer',
      });
    }
  }, [service, form]);

  const onSubmit = async (data: EditServiceForm) => {
    try {
      const response = await updateService(service.id, {
        name: service.name, // Keep the original name
        port: data.port,
        target_port: data.target_port,
        type: data.type,
        cluster_ip: service.cluster_ip,
        external_ip: service.external_ip,
        namespace_id: service.namespace_id,
        deployment_id: service.deployment_id,
      });

      if (response?.status) {
        toast.success('Service updated successfully');
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to update service');
      }
    } catch (error) {
      console.error('Error updating service:', error);
      toast.error('Failed to update service');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Service</DialogTitle>
          <DialogDescription>
            Update the port, target port, and type for service "{service.name}".
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="port"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Port</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="80"
                      {...field}
                      type="text"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="target_port"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target Port</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="8080"
                      {...field}
                      type="text"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select service type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ClusterIP">ClusterIP</SelectItem>
                      <SelectItem value="NodePort">NodePort</SelectItem>
                      <SelectItem value="LoadBalancer">LoadBalancer</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Update Service
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
