'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { useServiceStore } from '@/store/service/action';
import { CreateServiceRequest, UpdateServiceRequest } from '@/store/service/type';

/**
 * Example component demonstrating how to use the Service API endpoints
 * This component shows how to:
 * - Create a new service (POST /api/v1/services)
 * - Update an existing service (PUT /api/v1/services/:id)
 * - Delete a service (DELETE /api/v1/services/:id)
 * - Fetch services with filters
 */
export function ServiceExample() {
  const {
    services,
    selectedService,
    loading,
    creating,
    updating,
    deleting,
    fetchServices,
    fetchService,
    createService,
    updateService,
    deleteService,
  } = useServiceStore();

  const [formData, setFormData] = useState<CreateServiceRequest>({
    name: 'web-service',
    port: '80',
    target_port: '8080',
    type: 'ClusterIP',
    cluster_ip: '0.0.0.0',
    external_ip: '0.0.0.0',
    namespace_id: 1,
    deployment_id: 1,
  });

  // Fetch services on component mount
  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  const handleCreateService = async () => {
    try {
      const response = await createService(formData);
      if (response?.status) {
        toast.success('Service created successfully');
        // Refresh the services list
        fetchServices();
      } else {
        toast.error('Failed to create service');
      }
    } catch (error) {
      console.error('Error creating service:', error);
      toast.error('Failed to create service');
    }
  };

  const handleUpdateService = async (serviceId: number) => {
    try {
      const updateData: UpdateServiceRequest = {
        ...formData,
        name: `${formData.name}-updated`,
      };
      
      const response = await updateService(serviceId, updateData);
      if (response?.status) {
        toast.success('Service updated successfully');
        // Refresh the services list
        fetchServices();
      } else {
        toast.error('Failed to update service');
      }
    } catch (error) {
      console.error('Error updating service:', error);
      toast.error('Failed to update service');
    }
  };

  const handleDeleteService = async (serviceId: number) => {
    try {
      const response = await deleteService(serviceId);
      if (response?.status) {
        toast.success('Service deleted successfully');
        // Services list is automatically updated by the store
      } else {
        toast.error('Failed to delete service');
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  const handleFetchService = async (serviceId: number) => {
    try {
      await fetchService(serviceId);
      toast.success('Service details fetched');
    } catch (error) {
      console.error('Error fetching service:', error);
      toast.error('Failed to fetch service details');
    }
  };

  const handleInputChange = (field: keyof CreateServiceRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Service API Example</h1>
      
      {/* Create Service Form */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <h2 className="text-lg font-semibold mb-4">Create New Service</h2>
        <div className="grid grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="Service Name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className="border p-2 rounded"
          />
          <input
            type="text"
            placeholder="Port"
            value={formData.port}
            onChange={(e) => handleInputChange('port', e.target.value)}
            className="border p-2 rounded"
          />
          <input
            type="text"
            placeholder="Target Port"
            value={formData.target_port}
            onChange={(e) => handleInputChange('target_port', e.target.value)}
            className="border p-2 rounded"
          />
          <select
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
            className="border p-2 rounded"
          >
            <option value="ClusterIP">ClusterIP</option>
            <option value="NodePort">NodePort</option>
            <option value="LoadBalancer">LoadBalancer</option>
          </select>
          <input
            type="number"
            placeholder="Namespace ID"
            value={formData.namespace_id}
            onChange={(e) => handleInputChange('namespace_id', parseInt(e.target.value))}
            className="border p-2 rounded"
          />
          <input
            type="number"
            placeholder="Deployment ID"
            value={formData.deployment_id}
            onChange={(e) => handleInputChange('deployment_id', parseInt(e.target.value))}
            className="border p-2 rounded"
          />
        </div>
        <button
          onClick={handleCreateService}
          disabled={creating}
          className="mt-4 bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {creating ? 'Creating...' : 'Create Service'}
        </button>
      </div>

      {/* Services List */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4">Services List</h2>
        {loading ? (
          <p>Loading services...</p>
        ) : (
          <div className="space-y-2">
            {services.map((service) => (
              <div key={service.id} className="border p-3 rounded flex justify-between items-center">
                <div>
                  <h3 className="font-medium">{service.name}</h3>
                  <p className="text-sm text-gray-600">
                    {service.type} - {service.port}:{service.target_port}
                  </p>
                </div>
                <div className="space-x-2">
                  <button
                    onClick={() => handleFetchService(service.id)}
                    className="bg-green-500 text-white px-3 py-1 rounded text-sm"
                  >
                    View
                  </button>
                  <button
                    onClick={() => handleUpdateService(service.id)}
                    disabled={updating}
                    className="bg-yellow-500 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
                  >
                    {updating ? 'Updating...' : 'Update'}
                  </button>
                  <button
                    onClick={() => handleDeleteService(service.id)}
                    disabled={deleting}
                    className="bg-red-500 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
                  >
                    {deleting ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            ))}
            {services.length === 0 && !loading && (
              <p className="text-gray-500">No services found</p>
            )}
          </div>
        )}
      </div>

      {/* Selected Service Details */}
      {selectedService && (
        <div className="bg-white p-4 rounded-lg shadow mt-6">
          <h2 className="text-lg font-semibold mb-4">Selected Service Details</h2>
          <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
            {JSON.stringify(selectedService, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
