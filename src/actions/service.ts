'use server';

import Http from '@/lib/http';

export interface CreateServiceRequest {
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
}

export interface UpdateServiceRequest {
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
}

export async function createService(data: CreateServiceRequest) {
  try {
    const response = await Http.post('/services', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateService(id: number, data: UpdateServiceRequest) {
  try {
    const response = await Http.put(`/services/${id}`, data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteService(id: number) {
  try {
    const response = await Http.delete(`/services/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getService(id: number) {
  try {
    const response = await Http.get(`/services/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getServices(filters?: {
  namespace_id?: number;
  deployment_id?: number;
  name?: string;
  type?: string;
}) {
  try {
    const searchParams = new URLSearchParams();

    if (filters?.namespace_id) {
      searchParams.append('namespace_id', filters.namespace_id.toString());
    }
    if (filters?.deployment_id) {
      searchParams.append('deployment_id', filters.deployment_id.toString());
    }
    if (filters?.name) {
      searchParams.append('name', filters.name);
    }
    if (filters?.type) {
      searchParams.append('type', filters.type);
    }

    const url = `/services${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
