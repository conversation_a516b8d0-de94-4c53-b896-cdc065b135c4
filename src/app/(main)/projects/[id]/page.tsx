'use client';

import {
  ArrowLeft,
  Calendar,
  Clock,
  Container,
  Server,
  Globe,
  ExternalLink,
  Settings,
  Activity,
  Database,
  Network,
  Shield,
  Pencil,
  Plus,
  ChevronDown,
  Play,
  Trash2,
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState, useMemo } from 'react';
import { Toaster, toast } from 'sonner';

import { deleteService } from '@/actions/service';
import { ConfirmActionModal } from '@/components/confirm-action-modal';
import { ConfirmDeleteModal } from '@/components/confirm-delete-modal';
import { CreateDeploymentModal } from '@/components/main/deployment/create-deployment-modal';
import { CreateEnvironmentModal } from '@/components/main/deployment/create-environment-modal';
import { EditDeploymentModal } from '@/components/main/deployment/edit-deployment-modal';
import { EnvironmentList } from '@/components/main/deployment/environment-list';
import { MainHeader } from '@/components/main/layout/main-header';
import { EditServiceModal } from '@/components/main/service/edit-service-modal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { useDeploymentStore } from '@/store/deployment/action';
import { useOperationStore } from '@/store/operation/action';
import { useProjectStore } from '@/store/project/action';
import { calculateProjectStatus } from '@/utils/calculateProjectStatus';
import { getBadgeVariant } from '@/utils/getVariant';

export default function ProjectDetailPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = parseInt(params.id as string);

  const { selectedProject, loading, fetchProject } = useProjectStore();
  const { createOperation, loading: operationLoading } = useOperationStore();
  const { deleteDeployment } = useDeploymentStore();
  const [isClient, setIsClient] = useState(false);
  const [editingDeployment, setEditingDeployment] = useState<number | null>(
    null
  );

  const [createEnvDeploymentId, setCreateEnvDeploymentId] = useState<
    number | null
  >(null);
  const [createDeploymentOpen, setCreateDeploymentOpen] = useState(false);
  const [deletingDeployment, setDeletingDeployment] = useState<{
    id: number;
    name: string;
  } | null>(null);
  const [expandedEnvSections, setExpandedEnvSections] = useState<Set<number>>(
    new Set()
  );
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [currentAction, setCurrentAction] = useState<{
    type: 'publish' | 'unpublish';
  } | null>(null);
  const [editingService, setEditingService] = useState<{
    id: number;
    name: string;
    port: string;
    target_port: string;
    type: string;
    namespace_id: number;
    deployment_id: number;
    cluster_ip?: string;
    external_ip?: string;
  } | null>(null);
  const [deletingService, setDeletingService] = useState<{
    id: number;
    name: string;
  } | null>(null);

  useEffect(() => {
    setIsClient(true);
    if (projectId) {
      fetchProject(projectId);
    }
  }, [projectId, fetchProject]);

  const handleToggleActive = (isActive: boolean) => {
    // TODO: Implement toggle active state API call
    console.log(
      `Toggle project ${projectId} to ${isActive ? 'active' : 'inactive'}`
    );
  };

  const handleEditDeployment = (deploymentId: number) => {
    setEditingDeployment(deploymentId);
  };

  const handleEditSuccess = () => {
    // Re-fetch project details after successful edit
    fetchProject(projectId);
    setEditingDeployment(null);
  };

  const handleCreateEnvironment = (deploymentId: number) => {
    setCreateEnvDeploymentId(deploymentId);
  };

  const handleEnvironmentSuccess = () => {
    // Re-fetch project details after successful environment creation
    fetchProject(projectId);
  };

  const handleCreateDeployment = () => {
    setCreateDeploymentOpen(true);
  };

  const handleCreateDeploymentSuccess = () => {
    // Re-fetch project details after successful deployment creation
    fetchProject(projectId);
    setCreateDeploymentOpen(false);
  };

  const handleDeleteDeployment = (
    deploymentId: number,
    deploymentName: string
  ) => {
    setDeletingDeployment({ id: deploymentId, name: deploymentName });
  };

  const confirmDeleteDeployment = async () => {
    if (!deletingDeployment) {
      return;
    }

    try {
      const response = await deleteDeployment(deletingDeployment.id);
      if (response?.status) {
        toast.success('Deployment deleted successfully');
        setDeletingDeployment(null);
        // Re-fetch project details after successful deletion
        fetchProject(projectId);
      } else {
        toast.error('Failed to delete deployment');
      }
    } catch (error) {
      console.error('Error deleting deployment:', error);
      toast.error('Failed to delete deployment');
    }
  };

  const toggleEnvSection = (deploymentId: number) => {
    setExpandedEnvSections(prev =>
      prev.has(deploymentId)
        ? new Set([...prev].filter(id => id !== deploymentId))
        : new Set(prev.add(deploymentId))
    );
  };

  const handleEditService = (service: {
    id: number;
    name: string;
    port: string;
    target_port: string;
    type: string;
    namespace_id: number;
    deployment_id: number;
    cluster_ip?: string;
    external_ip?: string;
  }) => {
    setEditingService(service);
  };

  const handleEditServiceSuccess = () => {
    // Re-fetch project details after successful service edit
    fetchProject(projectId);
    setEditingService(null);
  };

  const handleDeleteService = (serviceId: number, serviceName: string) => {
    setDeletingService({ id: serviceId, name: serviceName });
  };

  const confirmDeleteService = async () => {
    if (!deletingService) {
      return;
    }

    try {
      const response: any = await deleteService(deletingService.id);
      if (response?.status) {
        toast.success('Service deleted successfully');
        setDeletingService(null);
        // Re-fetch project details after successful deletion
        fetchProject(projectId);
      } else {
        toast.error('Failed to delete service');
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  const handlePublish = () => {
    setCurrentAction({ type: 'publish' });
    setConfirmOpen(true);
  };

  const handleUnpublish = () => {
    setCurrentAction({ type: 'unpublish' });
    setConfirmOpen(true);
  };

  const confirmAction = async () => {
    if (!currentAction || !selectedProject) {
      return;
    }

    const { type } = currentAction;

    try {
      if (type === 'publish') {
        const success = await createOperation({
          cluster_id: selectedProject.cluster.id,
          namespace_id: projectId,
          method: 'apply',
        });

        if (success) {
          toast.success(
            'Operation created successfully - Project publishing initiated'
          );
          setTimeout(() => {
            fetchProject(projectId);
          }, 10000);
        } else {
          toast.error('Failed to create publish operation');
          return;
        }
      } else if (type === 'unpublish') {
        const success = await createOperation({
          cluster_id: selectedProject.cluster.id,
          namespace_id: projectId,
          method: 'destroy',
        });

        if (success) {
          toast.success(
            'Operation created successfully - Project destroy initiated'
          );
          setTimeout(() => {
            fetchProject(projectId);
          }, 10000);
        } else {
          toast.error('Failed to create destroy operation');
          return;
        }
      }

      fetchProject(projectId);
    } catch (error) {
      console.error('Error performing action:', error);
      toast.error('Failed to perform operation');
    } finally {
      setConfirmOpen(false);
      setCurrentAction(null);
    }
  };

  // Calculate project status using useMemo for performance
  const projectStatus = useMemo(() => {
    return calculateProjectStatus(selectedProject);
  }, [selectedProject]);

  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='h-96 bg-muted animate-pulse rounded-md flex items-center justify-center'>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='h-96 bg-muted animate-pulse rounded-md flex items-center justify-center'>
            <p className='text-muted-foreground'>Loading project details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedProject) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='flex items-center gap-4 mb-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.push('/projects')}
              className='flex items-center gap-2'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <h1 className='text-3xl font-medium tracking-tight'>
              Project Not Found
            </h1>
          </div>
          <Card>
            <CardContent className='flex items-center justify-center py-12'>
              <p className='text-muted-foreground'>
                The requested project could not be found.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      <MainHeader />
      <Toaster />
      <div className='flex flex-1 flex-col gap-6 p-4 pt-0'>
        {/* Header Section */}
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push('/projects')}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div className='flex-1'>
            <div className='flex items-center gap-3 mb-2'>
              <h1 className='text-3xl font-medium tracking-tight'>
                {selectedProject.name}
              </h1>
              <Badge variant='outline'>{selectedProject.type}</Badge>
              {selectedProject.type !== 'template' && (
                <Switch
                  checked={selectedProject.is_active}
                  onCheckedChange={handleToggleActive}
                />
              )}
            </div>
            <p className='text-muted-foreground'>
              {selectedProject.slug} • Created{' '}
              {new Date(selectedProject.created_at).toLocaleDateString()}
            </p>
          </div>
          <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                router.push(`/logs?event_id=${projectId}&event=namespace`)
              }
            >
              <Activity className='mr-2 h-4 w-4' />
              Logs
            </Button>
            {selectedProject.cluster.status.id === 3 &&
              projectStatus === 'active' &&
              selectedProject.type !== 'template' && (
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => router.push(`/projects/${projectId}/domains`)}
                >
                  <Globe className='mr-2 h-4 w-4' />
                  Domains
                </Button>
              )}
            {selectedProject.cluster.status.id === 3 &&
              selectedProject.type !== 'template' &&
              (projectStatus === 'maintenance' ||
                projectStatus === 'unpublished') && (
                <Button
                  variant='default'
                  size='sm'
                  onClick={handlePublish}
                  disabled={operationLoading}
                >
                  <Play className='mr-2 h-4 w-4' />
                  Publish
                </Button>
              )}
            {projectStatus === 'active' &&
              selectedProject.type !== 'template' && (
                <Button size='sm' onClick={() => handleUnpublish()}>
                  <Trash2 className='mr-2 h-4 w-4' />
                  Destroy
                </Button>
              )}
          </div>
        </div>

        {/* Project Overview Cards */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Deployments</CardTitle>
              <Container className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedProject.deployments?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Services</CardTitle>
              <Server className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedProject.services?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Ingress</CardTitle>
              <Globe className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {selectedProject.ingress?.length || 0}
              </div>
            </CardContent>
          </Card>
          {selectedProject.type !== 'template' && (
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Status</CardTitle>
                <Activity className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  <Badge variant={getBadgeVariant(projectStatus)}>
                    {projectStatus}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Left Column - Project Info & Cluster */}
          <div className='space-y-6'>
            {/* Project Information */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Settings className='h-5 w-5' />
                  Project Information
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='font-medium text-muted-foreground'>
                      Name:
                    </span>
                    <div className='font-medium'>{selectedProject.name}</div>
                  </div>
                  <div>
                    <span className='font-medium text-muted-foreground'>
                      Slug:
                    </span>
                    <div className='font-medium'>{selectedProject.slug}</div>
                  </div>
                  <div>
                    <span className='font-medium text-muted-foreground'>
                      Type:
                    </span>
                    <div>
                      <Badge variant='outline'>{selectedProject.type}</Badge>
                    </div>
                  </div>
                  {selectedProject.type !== 'template' && (
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Status:
                      </span>
                      <div>
                        <Badge variant={getBadgeVariant(projectStatus)}>
                          {projectStatus}
                        </Badge>
                      </div>
                    </div>
                  )}
                </div>
                <Separator />
                <div className='grid grid-cols-1 gap-4 text-sm'>
                  <div className='flex items-center gap-2'>
                    <Calendar className='h-4 w-4 text-muted-foreground' />
                    <span className='font-medium text-muted-foreground'>
                      Created:
                    </span>
                    <span>
                      {new Date(
                        selectedProject.created_at
                      ).toLocaleDateString()}
                    </span>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Clock className='h-4 w-4 text-muted-foreground' />
                    <span className='font-medium text-muted-foreground'>
                      Updated:
                    </span>
                    <span>
                      {new Date(
                        selectedProject.updated_at
                      ).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Cluster Information */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Database className='h-5 w-5' />
                  Cluster Information
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='font-medium'>
                      {selectedProject.cluster.name}
                    </span>
                    <Badge
                      variant={getBadgeVariant(selectedProject.cluster.status)}
                    >
                      {selectedProject.cluster.status.name}
                    </Badge>
                  </div>
                  <div className='grid grid-cols-2 gap-4 text-sm'>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Region:
                      </span>
                      <div>{selectedProject.cluster.region}</div>
                    </div>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Size:
                      </span>
                      <div>{selectedProject.cluster.size}</div>
                    </div>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Pool:
                      </span>
                      <div>{selectedProject.cluster.pool_name}</div>
                    </div>
                    <div>
                      <span className='font-medium text-muted-foreground'>
                        Nodes:
                      </span>
                      <div>{selectedProject.cluster.node_count}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Deployments, Services, Ingress */}
          <div className='lg:col-span-2 space-y-6'>
            {/* Deployments */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <Container className='h-5 w-5' />
                    Deployments ({selectedProject.deployments?.length || 0})
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleCreateDeployment}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4' />
                    Add Deployment
                  </Button>
                </CardTitle>
              </CardHeader>
              {selectedProject.deployments &&
              selectedProject.deployments.length > 0 ? (
                <CardContent>
                  <div className='space-y-4'>
                    {selectedProject.deployments.map(deployment => (
                      <div
                        key={deployment.id}
                        className='border rounded-lg p-4 space-y-3'
                      >
                        <div className='flex items-center justify-between'>
                          <h3 className='font-medium'>{deployment.name}</h3>
                          <div className='flex items-center gap-2'>
                            {selectedProject.type !== 'template' && (
                              <Badge
                                variant={getBadgeVariant(deployment.status)}
                              >
                                {deployment.status.name}
                              </Badge>
                            )}
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() =>
                                handleEditDeployment(deployment.id)
                              }
                              className='p-1 h-5 w-5'
                              title='Edit Deployment'
                            >
                              <Pencil className='h-2 w-2' />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() =>
                                handleDeleteDeployment(
                                  deployment.id,
                                  deployment.name
                                )
                              }
                              className='p-1 h-5 w-5 text-destructive hover:text-destructive'
                              title='Delete Deployment'
                            >
                              <Trash2 className='h-2 w-2' />
                            </Button>
                          </div>
                        </div>

                        <div className='grid grid-cols-2 md:grid-cols-3 gap-4 text-sm'>
                          <div>
                            <span className='font-medium text-muted-foreground'>
                              Image:
                            </span>
                            <div className='font-mono text-xs bg-muted px-2 py-1 rounded mt-1'>
                              {deployment.image}
                            </div>
                          </div>
                          <div>
                            <span className='font-medium text-muted-foreground'>
                              Port:
                            </span>
                            <div>{deployment.container_port}</div>
                          </div>
                          <div>
                            <span className='font-medium text-muted-foreground'>
                              Replicas:
                            </span>
                            <div>{deployment.replicas}</div>
                          </div>
                        </div>

                        {/* Environment Variables Section */}
                        <Collapsible
                          open={expandedEnvSections.has(deployment.id)}
                          onOpenChange={() => toggleEnvSection(deployment.id)}
                        >
                          <div className='border rounded-lg bg-muted/30'>
                            <CollapsibleTrigger asChild>
                              <div className='flex items-center justify-between p-3 cursor-pointer hover:bg-muted/50 transition-colors'>
                                <div className='flex items-center gap-2'>
                                  <ChevronDown
                                    className={`h-4 w-4 text-muted-foreground transition-transform ${
                                      expandedEnvSections.has(deployment.id)
                                        ? 'rotate-180'
                                        : ''
                                    }`}
                                  />
                                  <span className='font-medium text-sm text-muted-foreground'>
                                    Environment Variables (
                                    {deployment.environments?.length || 0})
                                  </span>
                                </div>
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={e => {
                                    e.stopPropagation();
                                    handleCreateEnvironment(deployment.id);
                                  }}
                                  className='h-6 w-6 p-0'
                                  title='Add Environment Variable'
                                >
                                  <Plus className='h-3 w-3' />
                                </Button>
                              </div>
                            </CollapsibleTrigger>
                            <CollapsibleContent className='px-3 pb-3'>
                              {deployment.environments &&
                              deployment.environments.length > 0 ? (
                                <EnvironmentList
                                  environments={deployment.environments}
                                  deploymentId={deployment.id}
                                  onSuccess={handleEnvironmentSuccess}
                                />
                              ) : (
                                <div className='text-sm text-muted-foreground italic bg-muted/30 rounded-lg p-4 text-center'>
                                  No environment variables configured
                                  <div className='text-xs mt-1'>
                                    Click the + button above to add your first
                                    environment variable
                                  </div>
                                </div>
                              )}
                            </CollapsibleContent>
                          </div>
                        </Collapsible>
                      </div>
                    ))}
                  </div>
                </CardContent>
              ) : (
                <CardContent>
                  <div className='text-center py-8'>
                    <Container className='h-12 w-12 mx-auto text-muted-foreground mb-4' />
                    <h3 className='text-lg font-medium mb-2'>No deployments</h3>
                    <p className='text-muted-foreground mb-4'>
                      Get started by creating your first deployment.
                    </p>
                    <Button
                      variant='default'
                      onClick={handleCreateDeployment}
                      className='flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4' />
                      Create Deployment
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Services */}
            {selectedProject.services &&
              selectedProject.services.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <Network className='h-5 w-5' />
                      Services ({selectedProject.services.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-4'>
                      {selectedProject.services.map(service => (
                        <div
                          key={service.id}
                          className='border rounded-lg p-4 space-y-3'
                        >
                          <div className='flex items-center justify-between'>
                            <h3 className='font-medium'>{service.name}</h3>
                            <div className='flex items-center gap-2'>
                              {selectedProject.type !== 'template' && (
                                <Badge
                                  variant={getBadgeVariant(service.status)}
                                >
                                  {service.status.name}
                                </Badge>
                              )}
                              {selectedProject.type !== 'template' && (
                                <div className='flex items-center gap-1'>
                                  <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                      handleEditService({
                                        id: service.id,
                                        name: service.name,
                                        port: service.port,
                                        target_port: service.target_port,
                                        type: service.type,
                                        namespace_id: service.namespace_id || 0,
                                        deployment_id:
                                          service.deployment_id || 0,
                                        cluster_ip: service.cluster_ip,
                                        external_ip: service.external_ip,
                                      })
                                    }
                                    className='h-8 w-8 p-0'
                                  >
                                    <Pencil className='h-3 w-3' />
                                  </Button>
                                  <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() =>
                                      handleDeleteService(
                                        service.id,
                                        service.name
                                      )
                                    }
                                    className='h-8 w-8 p-0 text-destructive hover:text-destructive'
                                  >
                                    <Trash2 className='h-3 w-3' />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                            <div>
                              <span className='font-medium text-muted-foreground'>
                                Type:
                              </span>
                              <div>{service.type}</div>
                            </div>
                            <div>
                              <span className='font-medium text-muted-foreground'>
                                Ports:
                              </span>
                              <div>
                                {service.port} → {service.target_port}
                              </div>
                            </div>
                            {service.cluster_ip && (
                              <div>
                                <span className='font-medium text-muted-foreground'>
                                  Cluster IP:
                                </span>
                                <div className='font-mono text-xs'>
                                  {service.cluster_ip}
                                </div>
                              </div>
                            )}
                            {service.external_ip && (
                              <div>
                                <span className='font-medium text-muted-foreground'>
                                  External IP:
                                </span>
                                <div className='font-mono text-xs'>
                                  {service.external_ip}
                                </div>
                              </div>
                            )}
                          </div>

                          {selectedProject.type !== 'template' &&
                            service.ingress_specs &&
                            service.ingress_specs.length > 0 && (
                              <div>
                                <span className='font-medium text-sm text-muted-foreground'>
                                  Ingress Specs:
                                </span>
                                <div className='space-y-2 mt-2'>
                                  {service.ingress_specs.map(spec => (
                                    <div
                                      key={spec.id}
                                      className='flex items-center gap-2 text-sm bg-muted/30 rounded p-2'
                                    >
                                      <span className='text-blue-600 font-mono'>
                                        {spec.host}
                                        {spec.path}
                                      </span>
                                      <span className='text-muted-foreground'>
                                        :{spec.port}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

            {/* Ingress */}
            {selectedProject.ingress && selectedProject.ingress.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Shield className='h-5 w-5' />
                    Ingress ({selectedProject.ingress.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    {selectedProject.ingress.map(ingress => (
                      <div
                        key={ingress.id}
                        className='border rounded-lg p-4 space-y-3'
                      >
                        <div className='flex items-center justify-between'>
                          <h3 className='font-medium'>{ingress.name}</h3>
                          {selectedProject.type !== 'template' && (
                            <Badge variant={getBadgeVariant(ingress.status)}>
                              {ingress.status.name}
                            </Badge>
                          )}
                        </div>

                        <div className='text-sm'>
                          <span className='font-medium text-muted-foreground'>
                            Class:{' '}
                          </span>
                          <span className='font-mono bg-muted px-2 py-1 rounded'>
                            {ingress.class}
                          </span>
                        </div>

                        {selectedProject.type !== 'template' &&
                          ingress.ingress_specs &&
                          ingress.ingress_specs.length > 0 && (
                            <div>
                              <span className='font-medium text-sm text-muted-foreground'>
                                Routes:
                              </span>
                              <div className='space-y-2 mt-2'>
                                {ingress.ingress_specs.map(spec => (
                                  <div
                                    key={spec.id}
                                    className='bg-muted/30 rounded p-2'
                                  >
                                    <div className='flex items-center justify-between text-sm'>
                                      <a
                                        href={`https://${spec.host}${spec.path}`}
                                        target='_blank'
                                        rel='noopener noreferrer'
                                        className='text-blue-600 hover:text-blue-800 flex items-center gap-1 font-mono text-xs'
                                      >
                                        {spec.host}
                                        {spec.path}
                                        <ExternalLink className='h-3 w-3' />
                                      </a>
                                      <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                                        <span>:{spec.port}</span>
                                        {spec.service && (
                                          <span>
                                            → {spec.service.name}:
                                            {spec.service.port}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Edit Deployment Modal */}
        {editingDeployment !== null && (
          <EditDeploymentModal
            deploymentId={editingDeployment}
            onSuccess={handleEditSuccess}
            onClose={() => setEditingDeployment(null)}
          />
        )}

        {/* Create Deployment Modal */}
        <CreateDeploymentModal
          namespaceId={projectId}
          open={createDeploymentOpen}
          onOpenChange={setCreateDeploymentOpen}
          onSuccess={handleCreateDeploymentSuccess}
        />

        {/* Create Environment Modal */}
        {createEnvDeploymentId !== null && (
          <CreateEnvironmentModal
            deploymentId={createEnvDeploymentId}
            open={createEnvDeploymentId !== null}
            onOpenChange={open => {
              if (!open) {
                setCreateEnvDeploymentId(null);
              }
            }}
            onSuccess={handleEnvironmentSuccess}
          />
        )}

        {/* Delete Deployment Confirmation Modal */}
        {deletingDeployment && (
          <ConfirmDeleteModal
            open={deletingDeployment !== null}
            onOpenChange={open => {
              if (!open) {
                setDeletingDeployment(null);
              }
            }}
            onConfirm={confirmDeleteDeployment}
            title='Delete Deployment'
            description={`Are you sure you want to delete the deployment "${deletingDeployment.name}"? This action cannot be undone and will remove all associated resources.`}
          />
        )}

        {/* Confirm Action Modal */}
        <ConfirmActionModal
          open={confirmOpen}
          onOpenChange={setConfirmOpen}
          onConfirm={confirmAction}
          title={
            currentAction?.type === 'publish'
              ? 'Publish Project'
              : 'Destroy Project'
          }
          description={
            currentAction?.type === 'publish'
              ? 'Are you sure you want to publish this project ?'
              : 'Are you sure you want to destroy this project ?'
          }
          variant={
            currentAction?.type === 'publish' ? 'default' : 'destructive'
          }
          icon={
            currentAction?.type === 'publish' ? (
              <Play className='h-5 w-5 text-blue-600' />
            ) : (
              <Trash2 className='h-5 w-5 text-red-600' />
            )
          }
        />

        {/* Edit Service Modal */}
        {editingService && (
          <EditServiceModal
            service={editingService}
            open={editingService !== null}
            onOpenChange={open => {
              if (!open) {
                setEditingService(null);
              }
            }}
            onSuccess={handleEditServiceSuccess}
          />
        )}

        {/* Delete Service Confirmation Modal */}
        {deletingService && (
          <ConfirmDeleteModal
            open={deletingService !== null}
            onOpenChange={open => {
              if (!open) {
                setDeletingService(null);
              }
            }}
            onConfirm={confirmDeleteService}
            title='Delete Service'
            description={`Are you sure you want to delete the service "${deletingService.name}"? This action cannot be undone and will remove the service from the deployment.`}
          />
        )}
      </div>
    </div>
  );
}
