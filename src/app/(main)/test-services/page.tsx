'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { EditServiceModal } from '@/components/main/service/edit-service-modal';
import { ConfirmDeleteModal } from '@/components/confirm-delete-modal';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Pencil, Trash2, Network } from 'lucide-react';
import { deleteService } from '@/actions/service';

// Mock service data for testing
const mockServices = [
  {
    id: 1,
    name: 'web-service',
    port: '80',
    target_port: '8080',
    type: 'ClusterIP',
    cluster_ip: '********',
    external_ip: '*************',
    namespace_id: 1,
    deployment_id: 1,
    status: { id: 1, name: 'Running' },
  },
  {
    id: 2,
    name: 'api-service',
    port: '3000',
    target_port: '3000',
    type: 'NodePort',
    cluster_ip: '********',
    namespace_id: 1,
    deployment_id: 2,
    status: { id: 2, name: 'Pending' },
  },
];

export default function TestServicesPage() {
  const [services, setServices] = useState(mockServices);
  const [editingService, setEditingService] = useState<{
    id: number;
    name: string;
    port: string;
    target_port: string;
    type: string;
    namespace_id: number;
    deployment_id: number;
    cluster_ip?: string;
    external_ip?: string;
  } | null>(null);
  const [deletingService, setDeletingService] = useState<{
    id: number;
    name: string;
  } | null>(null);

  const getBadgeVariant = (status: { name: string }) => {
    switch (status.name.toLowerCase()) {
      case 'running':
        return 'default' as const;
      case 'pending':
        return 'secondary' as const;
      case 'failed':
        return 'destructive' as const;
      default:
        return 'outline' as const;
    }
  };

  const handleEditService = (service: typeof editingService) => {
    setEditingService(service);
  };

  const handleEditServiceSuccess = () => {
    toast.success('Service updated successfully (mock)');
    setEditingService(null);
    // In real implementation, this would refetch the project data
  };

  const handleDeleteService = (serviceId: number, serviceName: string) => {
    setDeletingService({ id: serviceId, name: serviceName });
  };

  const confirmDeleteService = async () => {
    if (!deletingService) return;

    try {
      // In real implementation, this would call the actual API
      // const response = await deleteService(deletingService.id);
      
      // Mock successful deletion
      setServices(prev => prev.filter(s => s.id !== deletingService.id));
      toast.success('Service deleted successfully (mock)');
      setDeletingService(null);
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Service Management Test</h1>
        <p className="text-muted-foreground">
          Test page for service edit and delete functionality
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            Services ({services.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {services.map(service => (
              <div
                key={service.id}
                className="border rounded-lg p-4 space-y-3"
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{service.name}</h3>
                  <div className="flex items-center gap-2">
                    <Badge variant={getBadgeVariant(service.status)}>
                      {service.status.name}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditService({
                          id: service.id,
                          name: service.name,
                          port: service.port,
                          target_port: service.target_port,
                          type: service.type,
                          namespace_id: service.namespace_id,
                          deployment_id: service.deployment_id,
                          cluster_ip: service.cluster_ip,
                          external_ip: service.external_ip,
                        })}
                        className="h-8 w-8 p-0"
                      >
                        <Pencil className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteService(service.id, service.name)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-muted-foreground">
                      Type:
                    </span>
                    <div>{service.type}</div>
                  </div>
                  <div>
                    <span className="font-medium text-muted-foreground">
                      Ports:
                    </span>
                    <div>
                      {service.port} → {service.target_port}
                    </div>
                  </div>
                  {service.cluster_ip && (
                    <div>
                      <span className="font-medium text-muted-foreground">
                        Cluster IP:
                      </span>
                      <div className="font-mono text-xs">
                        {service.cluster_ip}
                      </div>
                    </div>
                  )}
                  {service.external_ip && (
                    <div>
                      <span className="font-medium text-muted-foreground">
                        External IP:
                      </span>
                      <div className="font-mono text-xs">
                        {service.external_ip}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edit Service Modal */}
      {editingService && (
        <EditServiceModal
          service={editingService}
          open={editingService !== null}
          onOpenChange={open => {
            if (!open) {
              setEditingService(null);
            }
          }}
          onSuccess={handleEditServiceSuccess}
        />
      )}

      {/* Delete Service Confirmation Modal */}
      {deletingService && (
        <ConfirmDeleteModal
          open={deletingService !== null}
          onOpenChange={open => {
            if (!open) {
              setDeletingService(null);
            }
          }}
          onConfirm={confirmDeleteService}
          title="Delete Service"
          description={`Are you sure you want to delete the service "${deletingService.name}"? This action cannot be undone and will remove the service from the deployment.`}
        />
      )}
    </div>
  );
}
