export interface ServiceType {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
  status?: StatusType;
  ingress_specs?: IngressSpecType[];
}

export interface StatusType {
  id: number;
  name: string;
}

export interface IngressSpecType {
  id: number;
  host: string;
  path: string;
  port: number;
  service?: ServiceType;
}

export interface CreateServiceRequest {
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
}

export interface UpdateServiceRequest {
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
}

export interface ServiceFilters {
  namespace_id?: number;
  deployment_id?: number;
  name?: string;
  type?: string;
}

export interface ServiceStates {
  services: ServiceType[];
  selectedService: ServiceType | null;
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
}

export interface ServiceActions {
  setServices: (services: ServiceType[]) => void;
  setSelectedService: (service: ServiceType | null) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  fetchServices: (filters?: ServiceFilters) => Promise<void>;
  fetchService: (id: number) => Promise<void>;
  createService: (data: CreateServiceRequest) => Promise<any>;
  updateService: (id: number, data: UpdateServiceRequest) => Promise<any>;
  deleteService: (id: number) => Promise<any>;
}
